using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class orderSuccess : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否已登录
            if (Session["UserId"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadOrderInfo();
            }
        }

        private void LoadOrderInfo()
        {
            try
            {
                string orderIdStr = Request.QueryString["orderId"];
                if (string.IsNullOrEmpty(orderIdStr))
                {
                    Response.Redirect("allproduct.aspx");
                    return;
                }

                int orderId = Convert.ToInt32(orderIdStr);
                int customerId = Convert.ToInt32(Session["UserId"]);

                // 获取订单信息
                Order order = db.Order.FirstOrDefault(o => o.OrderId == orderId && o.CustomerId == customerId);
                if (order == null)
                {
                    lbl_message.Text = "订单不存在或无权访问！";
                    lbl_message.ForeColor = System.Drawing.Color.Red;
                    return;
                }

                // 显示订单基本信息
                lbl_orderId.Text = order.OrderId.ToString();
                lbl_orderDate.Text = order.OrderDate.ToString("yyyy-MM-dd HH:mm:ss");
                lbl_status.Text = order.Status;
                lbl_userName.Text = order.UserName;
                lbl_phone.Text = order.Phone;

                // 组合地址
                string address = order.Addr1;
                if (!string.IsNullOrEmpty(order.Addr2))
                {
                    address += ", " + order.Addr2;
                }
                if (!string.IsNullOrEmpty(order.City))
                {
                    address += ", " + order.City;
                }
                if (!string.IsNullOrEmpty(order.State))
                {
                    address += ", " + order.State;
                }
                if (!string.IsNullOrEmpty(order.Zip))
                {
                    address += " " + order.Zip;
                }
                lbl_address.Text = address;

                // 获取订单商品
                var orderItems = db.OrderItem.Where(oi => oi.OrderId == orderId).ToList();
                GridView1.DataSource = orderItems;
                GridView1.DataBind();

                // 计算总价
                decimal total = orderItems.Sum(item => item.TotalPrice ?? 0);
                lbl_total.Text = total.ToString("F2");
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载订单信息失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_continue_Click(object sender, EventArgs e)
        {
            Response.Redirect("allproduct.aspx");
        }

        protected void btn_myorders_Click(object sender, EventArgs e)
        {
            Response.Redirect("myOrders.aspx");
        }
    }
}
