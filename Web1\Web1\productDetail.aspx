﻿<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="productDetail.aspx.cs" Inherits="Web1.productDetail" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>商品详情</h2>

    <table border="1" cellpadding="10" cellspacing="0" style="width: 100%;">
        <tr>
            <td style="width: 200px; text-align: center;">
                <asp:Image ID="img_product" runat="server" Height="150px" Width="150px" />
            </td>
            <td style="vertical-align: top;">
                <table cellpadding="5" cellspacing="0">
                    <tr>
                        <td><strong>商品名称：</strong></td>
                        <td><asp:Label ID="lbl_name" runat="server" Text=""></asp:Label></td>
                    </tr>
                    <tr>
                        <td><strong>商品价格：</strong></td>
                        <td><asp:Label ID="lbl_price" runat="server" Text="" ForeColor="Red" Font-Size="16px"></asp:Label> 元</td>
                    </tr>
                    <tr>
                        <td><strong>库存数量：</strong></td>
                        <td><asp:Label ID="lbl_qty" runat="server" Text=""></asp:Label> 件</td>
                    </tr>
                    <tr>
                        <td><strong>商品描述：</strong></td>
                        <td><asp:Label ID="lbl_desc" runat="server" Text=""></asp:Label></td>
                    </tr>
                    <tr>
                        <td><strong>购买数量：</strong></td>
                        <td>
                            <asp:TextBox ID="txt_buyqty" runat="server" Text="1" Width="50px"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server"
                                ControlToValidate="txt_buyqty" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
                            <asp:RangeValidator ID="RangeValidator1" runat="server"
                                ControlToValidate="txt_buyqty" ErrorMessage="数量必须大于0"
                                MinimumValue="1" MaximumValue="999" Type="Integer" ForeColor="Red"></asp:RangeValidator>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <asp:Button ID="btn_addcart" runat="server" Text="加入购物车"
                                OnClick="btn_addcart_Click" BackColor="#007bff" ForeColor="White"
                                Font-Size="14px" Padding="8px 16px" />
                            <asp:Button ID="btn_back" runat="server" Text="返回商品列表"
                                OnClick="btn_back_Click" BackColor="#6c757d" ForeColor="White"
                                Font-Size="14px" Padding="8px 16px" CausesValidation="false" />
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <br />
    <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Green" Font-Size="14px"></asp:Label>

</asp:Content>
