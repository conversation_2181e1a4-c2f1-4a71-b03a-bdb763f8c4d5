using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class checkout : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否已登录
            if (Session["UserId"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadOrderItems();
                LoadUserInfo();
            }
        }

        private void LoadOrderItems()
        {
            try
            {
                int customerId = Convert.ToInt32(Session["UserId"]);
                var cartItems = db.CartItem.Where(c => c.CustomerId == customerId).ToList();

                if (cartItems.Count == 0)
                {
                    Response.Redirect("cart.aspx");
                    return;
                }

                GridView1.DataSource = cartItems;
                GridView1.DataBind();

                // 计算总价
                decimal total = cartItems.Sum(item => item.ListPrice * item.Qty);
                lbl_total.Text = total.ToString("F2");

                // 设置商品图片
                foreach (GridViewRow row in GridView1.Rows)
                {
                    if (row.RowType == DataControlRowType.DataRow)
                    {
                        CartItem item = cartItems[row.RowIndex];
                        Image imgProduct = (Image)row.FindControl("img_product");
                        if (imgProduct != null)
                        {
                            Product product = db.Product.Find(item.ProId);
                            if (product != null)
                            {
                                imgProduct.ImageUrl = product.Image;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载订单信息失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        private void LoadUserInfo()
        {
            try
            {
                // 预填充用户信息
                string userName = Session["UserName"]?.ToString() ?? "";
                txt_name.Text = userName;
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载用户信息失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            Response.Redirect("cart.aspx");
        }

        protected void btn_submit_Click(object sender, EventArgs e)
        {
            try
            {
                int customerId = Convert.ToInt32(Session["UserId"]);
                string userName = Session["UserName"]?.ToString() ?? "";

                // 获取购物车商品
                var cartItems = db.CartItem.Where(c => c.CustomerId == customerId).ToList();
                if (cartItems.Count == 0)
                {
                    lbl_message.Text = "购物车是空的！";
                    lbl_message.ForeColor = System.Drawing.Color.Red;
                    return;
                }

                // 检查库存
                foreach (var cartItem in cartItems)
                {
                    Product product = db.Product.Find(cartItem.ProId);
                    if (product == null || product.Qty < cartItem.Qty)
                    {
                        lbl_message.Text = $"商品 {cartItem.ProName} 库存不足！";
                        lbl_message.ForeColor = System.Drawing.Color.Red;
                        return;
                    }
                }

                // 创建订单
                Order order = new Order
                {
                    CustomerId = customerId,
                    UserName = userName,
                    OrderDate = DateTime.Now,
                    Addr1 = txt_addr1.Text.Trim(),
                    Addr2 = txt_addr2.Text.Trim(),
                    City = txt_city.Text.Trim(),
                    State = txt_state.Text.Trim(),
                    Zip = txt_zip.Text.Trim(),
                    Phone = txt_phone.Text.Trim(),
                    Status = "已提交"
                };

                db.Order.Add(order);
                db.SaveChanges(); // 保存订单以获取OrderId

                // 创建订单项并更新库存
                foreach (var cartItem in cartItems)
                {
                    OrderItem orderItem = new OrderItem
                    {
                        OrderId = order.OrderId,
                        ProName = cartItem.ProName,
                        ListPrice = cartItem.ListPrice,
                        Qty = cartItem.Qty,
                        TotalPrice = cartItem.ListPrice * cartItem.Qty
                    };

                    db.OrderItem.Add(orderItem);

                    // 更新商品库存
                    Product product = db.Product.Find(cartItem.ProId);
                    if (product != null)
                    {
                        product.Qty -= cartItem.Qty;
                    }

                    // 删除购物车项
                    db.CartItem.Remove(cartItem);
                }

                db.SaveChanges();

                // 跳转到订单成功页面
                Response.Redirect("orderSuccess.aspx?orderId=" + order.OrderId);
            }
            catch (Exception ex)
            {
                lbl_message.Text = "提交订单失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }
    }
}
