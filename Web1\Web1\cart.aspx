<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="cart.aspx.cs" Inherits="Web1.cart" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>我的购物车</h2>
    
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" 
        CellPadding="4" ForeColor="#333333" GridLines="None" Width="100%"
        OnRowCommand="GridView1_RowCommand" OnRowDataBound="GridView1_RowDataBound">
        <AlternatingRowStyle BackColor="White" />
        <Columns>
            <asp:BoundField DataField="CartItemId" HeaderText="ID" Visible="false" />
            <asp:TemplateField HeaderText="商品图片">
                <ItemTemplate>
                    <asp:Image ID="img_product" runat="server" Height="50px" Width="50px" />
                </ItemTemplate>
            </asp:TemplateField>
            <asp:BoundField DataField="ProName" HeaderText="商品名称" />
            <asp:BoundField DataField="ListPrice" HeaderText="单价" DataFormatString="{0:F2}" />
            <asp:TemplateField HeaderText="数量">
                <ItemTemplate>
                    <asp:TextBox ID="txt_qty" runat="server" Text='<%# Eval("Qty") %>' Width="50px"></asp:TextBox>
                    <asp:Button ID="btn_update" runat="server" Text="更新" 
                        CommandName="UpdateQty" CommandArgument='<%# Eval("CartItemId") %>' 
                        BackColor="#28a745" ForeColor="White" Font-Size="12px" />
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="小计">
                <ItemTemplate>
                    <asp:Label ID="lbl_subtotal" runat="server" 
                        Text='<%# String.Format("{0:F2}", Convert.ToDecimal(Eval("ListPrice")) * Convert.ToInt32(Eval("Qty"))) %>'></asp:Label>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="操作">
                <ItemTemplate>
                    <asp:Button ID="btn_delete" runat="server" Text="删除" 
                        CommandName="DeleteItem" CommandArgument='<%# Eval("CartItemId") %>' 
                        BackColor="#dc3545" ForeColor="White" Font-Size="12px"
                        OnClientClick="return confirm('确定要删除这个商品吗？');" />
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
        <EditRowStyle BackColor="#2461BF" />
        <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
        <RowStyle BackColor="#EFF3FB" />
        <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
        <SortedAscendingCellStyle BackColor="#F5F7FB" />
        <SortedAscendingHeaderStyle BackColor="#6D95E1" />
        <SortedDescendingCellStyle BackColor="#E9EBEF" />
        <SortedDescendingHeaderStyle BackColor="#4870BE" />
    </asp:GridView>
    
    <br />
    
    <div style="text-align: right; font-size: 16px; font-weight: bold;">
        总计：<asp:Label ID="lbl_total" runat="server" Text="0.00" ForeColor="Red"></asp:Label> 元
    </div>
    
    <br />
    
    <div style="text-align: center;">
        <asp:Button ID="btn_continue" runat="server" Text="继续购物" 
            OnClick="btn_continue_Click" BackColor="#6c757d" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" CausesValidation="false" />
        <asp:Button ID="btn_checkout" runat="server" Text="结算" 
            OnClick="btn_checkout_Click" BackColor="#007bff" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" />
        <asp:Button ID="btn_clear" runat="server" Text="清空购物车" 
            OnClick="btn_clear_Click" BackColor="#dc3545" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" CausesValidation="false"
            OnClientClick="return confirm('确定要清空购物车吗？');" />
    </div>
    
    <br />
    <asp:Label ID="lbl_message" runat="server" Text="" Font-Size="14px"></asp:Label>
    
    <asp:Panel ID="panel_empty" runat="server" Visible="false" style="text-align: center; padding: 50px;">
        <h3>购物车是空的</h3>
        <p>您还没有添加任何商品到购物车。</p>
        <asp:Button ID="btn_shop" runat="server" Text="去购物" 
            OnClick="btn_shop_Click" BackColor="#007bff" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" />
    </asp:Panel>

</asp:Content>
