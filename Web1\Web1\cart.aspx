<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="cart.aspx.cs" Inherits="Web1.cart" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2 style="margin: 0;">我的购物车</h2>
        <div>
            <asp:Label ID="lbl_itemCount" runat="server" Text="" ForeColor="#666" Font-Size="14px"></asp:Label>
        </div>
    </div>

    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False"
        CellPadding="8" ForeColor="#333333" GridLines="Horizontal" Width="100%"
        OnRowCommand="GridView1_RowCommand" OnRowDataBound="GridView1_RowDataBound"
        CssClass="cart-table" BorderStyle="None"
        EmptyDataText="购物车是空的"
        ShowHeaderWhenEmpty="false">
        <AlternatingRowStyle BackColor="White" />
        <Columns>
            <asp:BoundField DataField="CartItemId" HeaderText="ID" Visible="false" />
            <asp:TemplateField HeaderText="商品信息" HeaderStyle-Width="40%">
                <ItemTemplate>
                    <div style="display: flex; align-items: center;">
                        <asp:Image ID="img_product" runat="server" Height="60px" Width="60px"
                                   style="margin-right: 15px; border: 1px solid #ddd; border-radius: 4px;" />
                        <div>
                            <div style="font-weight: bold; font-size: 14px; margin-bottom: 5px;">
                                <asp:Label ID="lbl_proname" runat="server" Text='<%# Eval("ProName") %>'></asp:Label>
                            </div>
                            <div style="color: #666; font-size: 12px;">
                                商品编号：<asp:Label ID="lbl_proid" runat="server" Text='<%# Eval("ProId") %>'></asp:Label>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="单价" HeaderStyle-Width="15%" HeaderStyle-HorizontalAlign="Center" ItemStyle-HorizontalAlign="Center">
                <ItemTemplate>
                    <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">
                        ¥<asp:Label ID="lbl_price" runat="server" Text='<%# String.Format("{0:F2}", Eval("ListPrice")) %>'></asp:Label>
                    </span>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="数量" HeaderStyle-Width="20%" HeaderStyle-HorizontalAlign="Center" ItemStyle-HorizontalAlign="Center">
                <ItemTemplate>
                    <div style="display: flex; align-items: center; justify-content: center; gap: 5px;">
                        <asp:Button ID="btn_minus" runat="server" Text="-"
                            CommandName="DecreaseQty" CommandArgument='<%# Eval("CartItemId") %>'
                            style="width: 25px; height: 25px; border: 1px solid #ddd; background: #f8f9fa; cursor: pointer;" />
                        <asp:TextBox ID="txt_qty" runat="server" Text='<%# Eval("Qty") %>'
                                     Width="50px" style="text-align: center; border: 1px solid #ddd; height: 25px;"
                                     onkeypress="return event.charCode >= 48 && event.charCode <= 57"></asp:TextBox>
                        <asp:Button ID="btn_plus" runat="server" Text="+"
                            CommandName="IncreaseQty" CommandArgument='<%# Eval("CartItemId") %>'
                            style="width: 25px; height: 25px; border: 1px solid #ddd; background: #f8f9fa; cursor: pointer;" />
                        <asp:Button ID="btn_update" runat="server" Text="更新"
                            CommandName="UpdateQty" CommandArgument='<%# Eval("CartItemId") %>'
                            BackColor="#28a745" ForeColor="White" Font-Size="12px"
                            style="margin-left: 5px; padding: 3px 8px; border: none; border-radius: 3px;" />
                    </div>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="小计" HeaderStyle-Width="15%" HeaderStyle-HorizontalAlign="Center" ItemStyle-HorizontalAlign="Center">
                <ItemTemplate>
                    <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">
                        ¥<asp:Label ID="lbl_subtotal" runat="server"
                            Text='<%# String.Format("{0:F2}", Convert.ToDecimal(Eval("ListPrice")) * Convert.ToInt32(Eval("Qty"))) %>'></asp:Label>
                    </span>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="操作" HeaderStyle-Width="10%" HeaderStyle-HorizontalAlign="Center" ItemStyle-HorizontalAlign="Center">
                <ItemTemplate>
                    <asp:Button ID="btn_delete" runat="server" Text="删除"
                        CommandName="DeleteItem" CommandArgument='<%# Eval("CartItemId") %>'
                        BackColor="#dc3545" ForeColor="White" Font-Size="12px"
                        style="padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;"
                        OnClientClick="return confirm('确定要删除这个商品吗？');" />
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
        <EditRowStyle BackColor="#2461BF" />
        <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
        <RowStyle BackColor="#EFF3FB" />
        <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
        <SortedAscendingCellStyle BackColor="#F5F7FB" />
        <SortedAscendingHeaderStyle BackColor="#6D95E1" />
        <SortedDescendingCellStyle BackColor="#E9EBEF" />
        <SortedDescendingHeaderStyle BackColor="#4870BE" />
    </asp:GridView>

    <!-- 购物车统计信息 -->
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 20px; margin: 20px 0;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <span style="font-size: 14px; color: #666;">
                    已选择 <asp:Label ID="lbl_selectedCount" runat="server" Text="0" ForeColor="#007bff"></asp:Label> 件商品
                </span>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 14px; color: #666; margin-bottom: 5px;">
                    商品总价：¥<asp:Label ID="lbl_subtotal" runat="server" Text="0.00"></asp:Label>
                </div>
                <div style="font-size: 20px; font-weight: bold; color: #e74c3c;">
                    合计：¥<asp:Label ID="lbl_total" runat="server" Text="0.00"></asp:Label>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮区域 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
        <div>
            <asp:Button ID="btn_continue" runat="server" Text="继续购物"
                OnClick="btn_continue_Click" BackColor="#6c757d" ForeColor="White"
                Font-Size="14px" CausesValidation="false"
                style="padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;" />
            <asp:Button ID="btn_clear" runat="server" Text="清空购物车"
                OnClick="btn_clear_Click" BackColor="#dc3545" ForeColor="White"
                Font-Size="14px" CausesValidation="false"
                style="padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;"
                OnClientClick="return confirm('确定要清空购物车吗？');" />
        </div>
        <div>
            <asp:Button ID="btn_checkout" runat="server" Text="立即结算"
                OnClick="btn_checkout_Click" BackColor="#e74c3c" ForeColor="White"
                Font-Size="16px" Font-Bold="true"
                style="padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer;" />
        </div>
    </div>

    <br />
    <asp:Label ID="lbl_message" runat="server" Text="" Font-Size="14px"></asp:Label>

    <asp:Panel ID="panel_empty" runat="server" Visible="false" style="text-align: center; padding: 50px;">
        <h3>购物车是空的</h3>
        <p>您还没有添加任何商品到购物车。</p>
        <asp:Button ID="btn_shop" runat="server" Text="去购物"
            OnClick="btn_shop_Click" BackColor="#007bff" ForeColor="White"
            Font-Size="14px" Padding="8px 16px" />
    </asp:Panel>

</asp:Content>
