﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productDetail : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadProductDetail();
            }
        }

        private void LoadProductDetail()
        {
            try
            {
                string pid = Request.QueryString["pid"];
                if (string.IsNullOrEmpty(pid))
                {
                    Response.Redirect("allproduct.aspx");
                    return;
                }

                int productId = Convert.ToInt32(pid);
                Product product = db.Product.Find(productId);

                if (product != null)
                {
                    img_product.ImageUrl = product.Image;
                    lbl_name.Text = product.Name;
                    lbl_price.Text = product.ListPrice?.ToString("F2") ?? "0.00";
                    lbl_qty.Text = product.Qty.ToString();
                    lbl_desc.Text = product.Descn;

                    // 设置购买数量的最大值为库存数量
                    RangeValidator1.MaximumValue = product.Qty.ToString();
                }
                else
                {
                    lbl_message.Text = "商品不存在！";
                    lbl_message.ForeColor = System.Drawing.Color.Red;
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载商品信息失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_addcart_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查用户是否已登录
                if (Session["UserId"] == null)
                {
                    Response.Redirect("login.aspx");
                    return;
                }

                string pid = Request.QueryString["pid"];
                int productId = Convert.ToInt32(pid);
                int customerId = Convert.ToInt32(Session["UserId"]);
                int buyQty = Convert.ToInt32(txt_buyqty.Text);

                Product product = db.Product.Find(productId);
                if (product == null)
                {
                    lbl_message.Text = "商品不存在！";
                    lbl_message.ForeColor = System.Drawing.Color.Red;
                    return;
                }

                // 检查库存
                if (buyQty > product.Qty)
                {
                    lbl_message.Text = "购买数量超过库存！";
                    lbl_message.ForeColor = System.Drawing.Color.Red;
                    return;
                }

                // 检查购物车中是否已有该商品
                CartItem existingItem = db.CartItem.FirstOrDefault(c => c.CustomerId == customerId && c.ProId == productId);

                if (existingItem != null)
                {
                    // 如果已存在，更新数量
                    existingItem.Qty += buyQty;
                }
                else
                {
                    // 如果不存在，添加新的购物车项
                    CartItem newItem = new CartItem
                    {
                        CustomerId = customerId,
                        ProId = productId,
                        ProName = product.Name,
                        ListPrice = product.ListPrice ?? 0,
                        Qty = buyQty
                    };
                    db.CartItem.Add(newItem);
                }

                db.SaveChanges();
                lbl_message.Text = "商品已成功加入购物车！";
                lbl_message.ForeColor = System.Drawing.Color.Green;
                txt_buyqty.Text = "1"; // 重置购买数量
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加入购物车失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            Response.Redirect("allproduct.aspx");
        }
    }
}