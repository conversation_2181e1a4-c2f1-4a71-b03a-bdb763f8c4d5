﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productEdit : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string pid = Request.QueryString["pid"].ToString();

                Product p = db.Product.Find(int.Parse(pid));
                this.ddl_cate.SelectedValue = p.CategoryId.ToString();
                this.ddl_supp.SelectedValue = p.SuppId.ToString();
                this.txt_desp.Text = p.Descn;
                this.txt_list.Text = p.ListPrice.ToString();
                this.txt_name.Text = p.Name;
                this.txt_qty.Text = p.Qty.ToString();
                this.txt_unit.Text = p.UnitCost.ToString();

                this.Image1.ImageUrl = p.Image;

                this.TextBox1.Text = pid;

            }
        }



        protected void Button1_Click(object sender, EventArgs e)
        {
            //图片上传

            if (FileUpload1.HasFile)
            {
                //this.Label2.Text = FileUpload1.PostedFile.FileName;
                string filepath = FileUpload1.PostedFile.FileName;

                string filename = filepath.Substring(filepath.LastIndexOf("\\") + 1);
                string fileindex = filename.Substring(filename.LastIndexOf("."));

                if (fileindex == ".gif")
                {
                    //this.Label2.Text=  Server.MapPath("Prod_Images");
                    string newfilepath = Server.MapPath("Prod_Images") + filename;
                    this.FileUpload1.PostedFile.SaveAs(newfilepath);
                    this.Image1.ImageUrl = "Prod_Images//" + filename;
                }
                else
                {
                    this.Label2.Text = "图片格式必须为gif";
                }

            }
        }

        protected void Button2_Click(object sender, EventArgs e)
        {
            string pid = this.TextBox1.Text;
          Product p=  db.Product.Find(int.Parse(pid));
            p.CategoryId = int.Parse(this.ddl_cate.SelectedItem.Value);
            p.SuppId = int.Parse(this.ddl_supp.SelectedItem.Value);
            p.ListPrice = Convert.ToDecimal(this.txt_list.Text.Trim());
            p.UnitCost = Convert.ToDecimal(this.txt_unit.Text.Trim());
            p.Name = txt_name.Text.Trim();
            p.Descn = txt_desp.Text.Trim();
            p.Qty = Convert.ToInt16(txt_qty.Text);

            p.Image = this.Image1.ImageUrl;

            db.SaveChanges();
        }
    }
}