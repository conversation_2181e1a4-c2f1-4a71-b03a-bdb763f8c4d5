﻿<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="login.aspx.cs" Inherits="Web1.login" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>用户登录</h2>

    用户名:<asp:TextBox ID="txt_id" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_id" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    密码:<asp:TextBox ID="txt_pwd" runat="server" TextMode="Password"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_pwd" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="登录" />
    <asp:Button ID="Button2" runat="server" OnClick="Button2_Click" Text="注册" CausesValidation="false" />
    <br /><br />

    <asp:Label ID="Label1" runat="server" Text="" ForeColor="Red"></asp:Label>

</asp:Content>
