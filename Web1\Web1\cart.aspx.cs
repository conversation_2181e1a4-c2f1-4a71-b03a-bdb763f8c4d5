using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class cart : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否已登录
            if (Session["UserId"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadCartItems();
            }
        }

        private void LoadCartItems()
        {
            try
            {
                int customerId = Convert.ToInt32(Session["UserId"]);
                
                var cartItems = db.CartItem.Where(c => c.CustomerId == customerId).ToList();

                if (cartItems.Count == 0)
                {
                    GridView1.Visible = false;
                    panel_empty.Visible = true;
                    lbl_total.Text = "0.00";
                }
                else
                {
                    GridView1.Visible = true;
                    panel_empty.Visible = false;
                    GridView1.DataSource = cartItems;
                    GridView1.DataBind();
                    
                    CalculateTotal();
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载购物车失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        private void CalculateTotal()
        {
            decimal total = 0;
            foreach (GridViewRow row in GridView1.Rows)
            {
                Label lblSubtotal = (Label)row.FindControl("lbl_subtotal");
                if (lblSubtotal != null)
                {
                    total += Convert.ToDecimal(lblSubtotal.Text);
                }
            }
            lbl_total.Text = total.ToString("F2");
        }

        protected void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                CartItem item = (CartItem)e.Row.DataItem;
                
                // 设置商品图片
                Image imgProduct = (Image)e.Row.FindControl("img_product");
                if (imgProduct != null)
                {
                    Product product = db.Product.Find(item.ProId);
                    if (product != null)
                    {
                        imgProduct.ImageUrl = product.Image;
                    }
                }
            }
        }

        protected void GridView1_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int cartItemId = Convert.ToInt32(e.CommandArgument);

                if (e.CommandName == "UpdateQty")
                {
                    // 更新数量
                    GridViewRow row = (GridViewRow)((Button)e.CommandSource).NamingContainer;
                    TextBox txtQty = (TextBox)row.FindControl("txt_qty");
                    
                    int newQty = Convert.ToInt32(txtQty.Text);
                    if (newQty <= 0)
                    {
                        lbl_message.Text = "数量必须大于0！";
                        lbl_message.ForeColor = System.Drawing.Color.Red;
                        return;
                    }

                    CartItem cartItem = db.CartItem.Find(cartItemId);
                    if (cartItem != null)
                    {
                        // 检查库存
                        Product product = db.Product.Find(cartItem.ProId);
                        if (product != null && newQty > product.Qty)
                        {
                            lbl_message.Text = "数量超过库存！当前库存：" + product.Qty;
                            lbl_message.ForeColor = System.Drawing.Color.Red;
                            return;
                        }

                        cartItem.Qty = newQty;
                        db.SaveChanges();
                        
                        lbl_message.Text = "数量更新成功！";
                        lbl_message.ForeColor = System.Drawing.Color.Green;
                    }
                }
                else if (e.CommandName == "DeleteItem")
                {
                    // 删除商品
                    CartItem cartItem = db.CartItem.Find(cartItemId);
                    if (cartItem != null)
                    {
                        db.CartItem.Remove(cartItem);
                        db.SaveChanges();
                        
                        lbl_message.Text = "商品删除成功！";
                        lbl_message.ForeColor = System.Drawing.Color.Green;
                    }
                }

                LoadCartItems(); // 重新加载购物车
            }
            catch (Exception ex)
            {
                lbl_message.Text = "操作失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_continue_Click(object sender, EventArgs e)
        {
            Response.Redirect("allproduct.aspx");
        }

        protected void btn_checkout_Click(object sender, EventArgs e)
        {
            // 检查购物车是否为空
            int customerId = Convert.ToInt32(Session["UserId"]);
            var cartItems = db.CartItem.Where(c => c.CustomerId == customerId).ToList();
            
            if (cartItems.Count == 0)
            {
                lbl_message.Text = "购物车是空的，无法结算！";
                lbl_message.ForeColor = System.Drawing.Color.Red;
                return;
            }

            Response.Redirect("checkout.aspx");
        }

        protected void btn_clear_Click(object sender, EventArgs e)
        {
            try
            {
                int customerId = Convert.ToInt32(Session["UserId"]);
                var cartItems = db.CartItem.Where(c => c.CustomerId == customerId).ToList();
                
                foreach (var item in cartItems)
                {
                    db.CartItem.Remove(item);
                }
                
                db.SaveChanges();
                LoadCartItems();
                
                lbl_message.Text = "购物车已清空！";
                lbl_message.ForeColor = System.Drawing.Color.Green;
            }
            catch (Exception ex)
            {
                lbl_message.Text = "清空购物车失败：" + ex.Message;
                lbl_message.ForeColor = System.Drawing.Color.Red;
            }
        }

        protected void btn_shop_Click(object sender, EventArgs e)
        {
            Response.Redirect("allproduct.aspx");
        }
    }
}
