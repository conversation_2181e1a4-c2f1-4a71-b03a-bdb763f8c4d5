﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6CA5965B-FC7A-40B7-9E62-E30470F18127}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Web1</RootNamespace>
    <AssemblyName>Web1</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44362</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="allproduct.aspx" />
    <Content Include="cart.aspx" />
    <Content Include="checkout.aspx" />
    <Content Include="custAdd.aspx" />
    <Content Include="custList.aspx" />
    <Content Include="Images\arrow.gif" />
    <Content Include="Images\close.gif" />
    <Content Include="Images\google_logo.gif" />
    <Content Include="Images\logo.gif" />
    <Content Include="Images\maxsize.gif" />
    <Content Include="Images\minsize.gif" />
    <Content Include="Images\new_flash.gif" />
    <Content Include="Images\searchbutton.gif" />
    <Content Include="Images\spacer.gif" />
    <Content Include="Images\xml.gif" />
    <Content Include="index.aspx" />
    <Content Include="index_m.aspx" />
    <Content Include="login.aspx" />
    <Content Include="Model1.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Model1.Context.cs</LastGenOutput>
      <DependentUpon>Model1.edmx</DependentUpon>
    </Content>
    <Content Include="Model1.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Model1.edmx</DependentUpon>
      <LastGenOutput>Model1.cs</LastGenOutput>
    </Content>
    <Content Include="myinfo.aspx" />
    <Content Include="orderSuccess.aspx" />
    <Content Include="productAdd.aspx" />
    <Content Include="productAdd2.aspx" />
    <Content Include="productCate.aspx" />
    <Content Include="productDetail.aspx" />
    <Content Include="productEdit.aspx" />
    <Content Include="productList.aspx" />
    <Content Include="productList2.aspx" />
    <Content Include="productSearch.aspx" />
    <Content Include="Prod_Images\11.png" />
    <Content Include="Prod_Images\ant.gif" />
    <Content Include="Prod_Images\butterfly.gif" />
    <Content Include="Prod_Images\cat.gif" />
    <Content Include="Prod_Images\domestic.gif" />
    <Content Include="Prod_Images\eucalyptus.gif" />
    <Content Include="Prod_Images\flowerloving.gif" />
    <Content Include="Prod_Images\meno.gif" />
    <Content Include="Prod_Images\panda.gif" />
    <Content Include="Prod_Images\pointy.gif" />
    <Content Include="Prod_Images\Saola.png" />
    <Content Include="Prod_Images\zebra.gif" />
    <Content Include="userAdd.aspx" />
    <Content Include="Web.config" />
    <Content Include="WebForm1.aspx" />
    <Content Include="WebForm2.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="allproduct.aspx.cs">
      <DependentUpon>allproduct.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="allproduct.aspx.designer.cs">
      <DependentUpon>allproduct.aspx</DependentUpon>
    </Compile>
    <Compile Include="CartItem.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="cart.aspx.cs">
      <DependentUpon>cart.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cart.aspx.designer.cs">
      <DependentUpon>cart.aspx</DependentUpon>
    </Compile>
    <Compile Include="Category.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="checkout.aspx.cs">
      <DependentUpon>checkout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="checkout.aspx.designer.cs">
      <DependentUpon>checkout.aspx</DependentUpon>
    </Compile>
    <Compile Include="custAdd.aspx.cs">
      <DependentUpon>custAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="custAdd.aspx.designer.cs">
      <DependentUpon>custAdd.aspx</DependentUpon>
    </Compile>
    <Compile Include="custList.aspx.cs">
      <DependentUpon>custList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="custList.aspx.designer.cs">
      <DependentUpon>custList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="h.Master.cs">
      <DependentUpon>h.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="h.Master.designer.cs">
      <DependentUpon>h.Master</DependentUpon>
    </Compile>
    <Compile Include="index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="index_m.aspx.cs">
      <DependentUpon>index_m.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index_m.aspx.designer.cs">
      <DependentUpon>index_m.aspx</DependentUpon>
    </Compile>
    <Compile Include="login.aspx.cs">
      <DependentUpon>login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="login.aspx.designer.cs">
      <DependentUpon>login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Model1.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Model1.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="Model1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model1.edmx</DependentUpon>
    </Compile>
    <Compile Include="myinfo.aspx.cs">
      <DependentUpon>myinfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="myinfo.aspx.designer.cs">
      <DependentUpon>myinfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Order.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderItem.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="orderSuccess.aspx.cs">
      <DependentUpon>orderSuccess.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="orderSuccess.aspx.designer.cs">
      <DependentUpon>orderSuccess.aspx</DependentUpon>
    </Compile>
    <Compile Include="Product.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="productAdd.aspx.cs">
      <DependentUpon>productAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productAdd.aspx.designer.cs">
      <DependentUpon>productAdd.aspx</DependentUpon>
    </Compile>
    <Compile Include="productAdd2.aspx.cs">
      <DependentUpon>productAdd2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productAdd2.aspx.designer.cs">
      <DependentUpon>productAdd2.aspx</DependentUpon>
    </Compile>
    <Compile Include="productCate.aspx.cs">
      <DependentUpon>productCate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productCate.aspx.designer.cs">
      <DependentUpon>productCate.aspx</DependentUpon>
    </Compile>
    <Compile Include="productDetail.aspx.cs">
      <DependentUpon>productDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productDetail.aspx.designer.cs">
      <DependentUpon>productDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="productEdit.aspx.cs">
      <DependentUpon>productEdit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productEdit.aspx.designer.cs">
      <DependentUpon>productEdit.aspx</DependentUpon>
    </Compile>
    <Compile Include="productList.aspx.cs">
      <DependentUpon>productList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productList.aspx.designer.cs">
      <DependentUpon>productList.aspx</DependentUpon>
    </Compile>
    <Compile Include="productList2.aspx.cs">
      <DependentUpon>productList2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productList2.aspx.designer.cs">
      <DependentUpon>productList2.aspx</DependentUpon>
    </Compile>
    <Compile Include="productSearch.aspx.cs">
      <DependentUpon>productSearch.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="productSearch.aspx.designer.cs">
      <DependentUpon>productSearch.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="q.Master.cs">
      <DependentUpon>q.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="q.Master.designer.cs">
      <DependentUpon>q.Master</DependentUpon>
    </Compile>
    <Compile Include="Supplier.cs">
      <DependentUpon>Model1.tt</DependentUpon>
    </Compile>
    <Compile Include="userAdd.aspx.cs">
      <DependentUpon>userAdd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="userAdd.aspx.designer.cs">
      <DependentUpon>userAdd.aspx</DependentUpon>
    </Compile>
    <Compile Include="WebForm1.aspx.cs">
      <DependentUpon>WebForm1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebForm1.aspx.designer.cs">
      <DependentUpon>WebForm1.aspx</DependentUpon>
    </Compile>
    <Compile Include="WebForm2.aspx.cs">
      <DependentUpon>WebForm2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebForm2.aspx.designer.cs">
      <DependentUpon>WebForm2.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Model1.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>Model1.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Model1.edmx.diagram">
      <DependentUpon>Model1.edmx</DependentUpon>
    </Content>
    <Content Include="h.Master" />
    <None Include="packages.config" />
    <Content Include="q.Master" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>62849</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44362/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>