<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="orderSuccess.aspx.cs" Inherits="Web1.orderSuccess" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div style="text-align: center; padding: 50px;">
        <h2 style="color: green;">订单提交成功！</h2>
        
        <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 20px auto; width: 500px; border-radius: 5px;">
            <h3>订单信息</h3>
            <table cellpadding="5" cellspacing="0" style="margin: 0 auto;">
                <tr>
                    <td><strong>订单编号：</strong></td>
                    <td><asp:Label ID="lbl_orderId" runat="server" Text="" ForeColor="Blue"></asp:Label></td>
                </tr>
                <tr>
                    <td><strong>下单时间：</strong></td>
                    <td><asp:Label ID="lbl_orderDate" runat="server" Text=""></asp:Label></td>
                </tr>
                <tr>
                    <td><strong>订单状态：</strong></td>
                    <td><asp:Label ID="lbl_status" runat="server" Text="" ForeColor="Green"></asp:Label></td>
                </tr>
                <tr>
                    <td><strong>收货人：</strong></td>
                    <td><asp:Label ID="lbl_userName" runat="server" Text=""></asp:Label></td>
                </tr>
                <tr>
                    <td><strong>联系电话：</strong></td>
                    <td><asp:Label ID="lbl_phone" runat="server" Text=""></asp:Label></td>
                </tr>
                <tr>
                    <td><strong>收货地址：</strong></td>
                    <td><asp:Label ID="lbl_address" runat="server" Text=""></asp:Label></td>
                </tr>
            </table>
        </div>
        
        <h3>订单商品</h3>
        <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" 
            CellPadding="4" ForeColor="#333333" GridLines="None" Width="80%" 
            style="margin: 0 auto;">
            <AlternatingRowStyle BackColor="White" />
            <Columns>
                <asp:BoundField DataField="ProName" HeaderText="商品名称" />
                <asp:BoundField DataField="ListPrice" HeaderText="单价" DataFormatString="{0:F2}" />
                <asp:BoundField DataField="Qty" HeaderText="数量" />
                <asp:BoundField DataField="TotalPrice" HeaderText="小计" DataFormatString="{0:F2}" />
            </Columns>
            <EditRowStyle BackColor="#2461BF" />
            <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
            <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
            <RowStyle BackColor="#EFF3FB" />
            <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
            <SortedAscendingCellStyle BackColor="#F5F7FB" />
            <SortedAscendingHeaderStyle BackColor="#6D95E1" />
            <SortedDescendingCellStyle BackColor="#E9EBEF" />
            <SortedDescendingHeaderStyle BackColor="#4870BE" />
        </asp:GridView>
        
        <div style="text-align: right; font-size: 16px; font-weight: bold; margin: 20px auto; width: 80%;">
            订单总计：<asp:Label ID="lbl_total" runat="server" Text="0.00" ForeColor="Red"></asp:Label> 元
        </div>
        
        <div style="margin-top: 30px;">
            <p style="color: #666; font-size: 14px;">
                感谢您的购买！我们会尽快为您安排发货。<br />
                您可以在个人中心查看订单详情和物流信息。
            </p>
        </div>
        
        <div style="margin-top: 30px;">
            <asp:Button ID="btn_continue" runat="server" Text="继续购物" 
                OnClick="btn_continue_Click" BackColor="#007bff" ForeColor="White" 
                Font-Size="14px" Padding="8px 16px" />
            <asp:Button ID="btn_myorders" runat="server" Text="查看我的订单" 
                OnClick="btn_myorders_Click" BackColor="#28a745" ForeColor="White" 
                Font-Size="14px" Padding="8px 16px" />
        </div>
    </div>
    
    <asp:Label ID="lbl_message" runat="server" Text="" Font-Size="14px" style="display: block; text-align: center;"></asp:Label>

</asp:Content>
