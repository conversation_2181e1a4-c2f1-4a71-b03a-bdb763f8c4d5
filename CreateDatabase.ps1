# PowerShell script to create the MyPetShop database
$connectionString = "Server=(localdb)\MSSQLLocalDB;Integrated Security=true;"
$sqlScript = Get-Content "Web1\Database_Scripts\Shop_sql2.sql" -Raw

try {
    # Split the script by GO statements
    $batches = $sqlScript -split '\bGO\b'
    
    # Create connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to LocalDB successfully"
    
    foreach ($batch in $batches) {
        $batch = $batch.Trim()
        if ($batch -ne "") {
            try {
                $command = New-Object System.Data.SqlClient.SqlCommand($batch, $connection)
                $command.ExecuteNonQuery()
                Write-Host "Executed batch successfully"
            }
            catch {
                Write-Host "Error executing batch: $($_.Exception.Message)"
            }
        }
    }
    
    $connection.Close()
    Write-Host "Database creation completed successfully!"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}
