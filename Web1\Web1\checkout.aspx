<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="checkout.aspx.cs" Inherits="Web1.checkout" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>订单确认</h2>
    
    <h3>订单商品</h3>
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" 
        CellPadding="4" ForeColor="#333333" GridLines="None" Width="100%">
        <AlternatingRowStyle BackColor="White" />
        <Columns>
            <asp:TemplateField HeaderText="商品图片">
                <ItemTemplate>
                    <asp:Image ID="img_product" runat="server" Height="40px" Width="40px" />
                </ItemTemplate>
            </asp:TemplateField>
            <asp:BoundField DataField="ProName" HeaderText="商品名称" />
            <asp:BoundField DataField="ListPrice" HeaderText="单价" DataFormatString="{0:F2}" />
            <asp:BoundField DataField="Qty" HeaderText="数量" />
            <asp:TemplateField HeaderText="小计">
                <ItemTemplate>
                    <asp:Label ID="lbl_subtotal" runat="server" 
                        Text='<%# String.Format("{0:F2}", Convert.ToDecimal(Eval("ListPrice")) * Convert.ToInt32(Eval("Qty"))) %>'></asp:Label>
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
        <EditRowStyle BackColor="#2461BF" />
        <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
        <RowStyle BackColor="#EFF3FB" />
        <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
        <SortedAscendingCellStyle BackColor="#F5F7FB" />
        <SortedAscendingHeaderStyle BackColor="#6D95E1" />
        <SortedDescendingCellStyle BackColor="#E9EBEF" />
        <SortedDescendingHeaderStyle BackColor="#4870BE" />
    </asp:GridView>
    
    <div style="text-align: right; font-size: 16px; font-weight: bold; margin: 10px 0;">
        订单总计：<asp:Label ID="lbl_total" runat="server" Text="0.00" ForeColor="Red"></asp:Label> 元
    </div>
    
    <hr />
    
    <h3>收货信息</h3>
    <table cellpadding="5" cellspacing="0">
        <tr>
            <td><strong>收货人姓名：</strong></td>
            <td>
                <asp:TextBox ID="txt_name" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" 
                    ControlToValidate="txt_name" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td><strong>联系电话：</strong></td>
            <td>
                <asp:TextBox ID="txt_phone" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" 
                    ControlToValidate="txt_phone" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td><strong>收货地址1：</strong></td>
            <td>
                <asp:TextBox ID="txt_addr1" runat="server" Width="300px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" 
                    ControlToValidate="txt_addr1" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td><strong>收货地址2：</strong></td>
            <td>
                <asp:TextBox ID="txt_addr2" runat="server" Width="300px"></asp:TextBox>
            </td>
        </tr>
        <tr>
            <td><strong>城市：</strong></td>
            <td>
                <asp:TextBox ID="txt_city" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" 
                    ControlToValidate="txt_city" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td><strong>省份：</strong></td>
            <td>
                <asp:TextBox ID="txt_state" runat="server" Width="200px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" 
                    ControlToValidate="txt_state" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td><strong>邮编：</strong></td>
            <td>
                <asp:TextBox ID="txt_zip" runat="server" Width="100px"></asp:TextBox>
            </td>
        </tr>
    </table>
    
    <br />
    
    <div style="text-align: center;">
        <asp:Button ID="btn_back" runat="server" Text="返回购物车" 
            OnClick="btn_back_Click" BackColor="#6c757d" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" CausesValidation="false" />
        <asp:Button ID="btn_submit" runat="server" Text="提交订单" 
            OnClick="btn_submit_Click" BackColor="#28a745" ForeColor="White" 
            Font-Size="14px" Padding="8px 16px" />
    </div>
    
    <br />
    <asp:Label ID="lbl_message" runat="server" Text="" Font-Size="14px"></asp:Label>

</asp:Content>
