﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web519
{
    public partial class q : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否已登录，如果已登录则显示"登入到后台"链接
            if (Session["UserId"] != null)
            {
                string userName = Session["UserName"]?.ToString() ?? "";
                Label1.Text = "欢迎您，" + userName;
                HyperLink6.Visible = true; // 显示"登入到后台"链接
            }
            else
            {
                Label1.Text = "您还没有登录";
                HyperLink6.Visible = false; // 隐藏"登入到后台"链接
            }
        }

        protected void ImageButton1_Click(object sender, ImageClickEventArgs e)
        {
            string condi = this.TextBox1.Text;
            Response.Redirect("productSearch.aspx?pn="+condi);
        }
    }
}